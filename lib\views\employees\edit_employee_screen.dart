import 'package:flutter/material.dart';
import '../../models/employee.dart';
import '../../services/database/employees_service.dart';
import '../../constants/app_theme.dart';
import '../../widgets/common/form_field_widget.dart';

/// شاشة تعديل بيانات الموظف
class EditEmployeeScreen extends StatefulWidget {
  final Employee employee;

  const EditEmployeeScreen({
    super.key,
    required this.employee,
  });

  @override
  State<EditEmployeeScreen> createState() => _EditEmployeeScreenState();
}

class _EditEmployeeScreenState extends State<EditEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();
  final EmployeesService _employeesService = EmployeesService();

  // متحكمات النص
  late final TextEditingController _nameController;
  late final TextEditingController _phoneController;
  late final TextEditingController _emailController;
  late final TextEditingController _positionController;
  late final TextEditingController _departmentController;

  // حالة النموذج
  bool _isLoading = false;
  bool _isValidating = false;
  bool _hasChanges = false;

  // رسائل الخطأ
  String? _nameError;
  String? _phoneError;
  String? _emailError;
  String? _positionError;
  String? _departmentError;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  /// تهيئة متحكمات النص بالبيانات الحالية
  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.employee.name);
    _phoneController = TextEditingController(text: widget.employee.phone ?? '');
    _emailController = TextEditingController(text: widget.employee.email ?? '');
    _positionController =
        TextEditingController(text: widget.employee.position ?? '');
    _departmentController =
        TextEditingController(text: widget.employee.department ?? '');

    // مراقبة التغييرات
    _nameController.addListener(_onFieldChanged);
    _phoneController.addListener(_onFieldChanged);
    _emailController.addListener(_onFieldChanged);
    _positionController.addListener(_onFieldChanged);
    _departmentController.addListener(_onFieldChanged);
  }

  /// مراقبة تغييرات الحقول
  void _onFieldChanged() {
    final hasChanges = _nameController.text != widget.employee.name ||
        _phoneController.text != (widget.employee.phone ?? '') ||
        _emailController.text != (widget.employee.email ?? '') ||
        _positionController.text != (widget.employee.position ?? '') ||
        _departmentController.text != (widget.employee.department ?? '');

    if (hasChanges != _hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _positionController.dispose();
    _departmentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  /// التعامل مع محاولة الخروج
  Future<bool> _onWillPop() async {
    if (!_hasChanges) return true;

    final shouldPop = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغييرات غير محفوظة'),
        content:
            const Text('لديك تغييرات غير محفوظة. هل تريد الخروج بدون حفظ؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('البقاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('خروج بدون حفظ'),
          ),
        ],
      ),
    );

    return shouldPop ?? false;
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('تعديل ${widget.employee.name}'),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // مؤشر التغييرات غير المحفوظة
        if (_hasChanges)
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Center(
              child: Icon(
                Icons.circle,
                color: Colors.orange,
                size: 12,
              ),
            ),
          ),

        // زر الحفظ
        TextButton(
          onPressed: (_isLoading || !_hasChanges) ? null : _saveEmployee,
          child: Text(
            'حفظ',
            style: TextStyle(
              color:
                  (_isLoading || !_hasChanges) ? Colors.white54 : Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الموظف الحالي
            _buildEmployeeInfoCard(),

            const SizedBox(height: 16),

            // بطاقة المعلومات الأساسية
            _buildBasicInfoCard(),

            const SizedBox(height: 16),

            // بطاقة معلومات الاتصال
            _buildContactInfoCard(),

            const SizedBox(height: 16),

            // بطاقة معلومات العمل
            _buildWorkInfoCard(),

            const SizedBox(height: 24),

            // أزرار الإجراءات
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات الموظف الحالي
  Widget _buildEmployeeInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: AppTheme.primaryColor.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // الحروف الأولى
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Center(
                child: Text(
                  widget.employee.initials,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // معلومات الموظف
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.employee.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  if (widget.employee.position != null)
                    Text(
                      widget.employee.position!,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  Text(
                    'تاريخ الإضافة: ${widget.employee.formattedCreatedAt}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات الأساسية
  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'المعلومات الأساسية',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // حقل الاسم
            FormFieldWidget(
              controller: _nameController,
              label: 'الاسم الكامل',
              hintText: 'أدخل اسم الموظف الكامل',
              prefixIcon: Icons.person_outline,
              validator: (value) => _nameError,
              onChanged: (_) => _validateName(),
              textInputAction: TextInputAction.next,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات الاتصال
  Widget _buildContactInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.contact_phone,
                  color: AppTheme.secondaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الاتصال',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.secondaryColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // حقل رقم الهاتف
            FormFieldWidget(
              controller: _phoneController,
              label: 'رقم الهاتف',
              hintText: 'أدخل رقم الهاتف (اختياري)',
              prefixIcon: Icons.phone_outlined,
              keyboardType: TextInputType.phone,
              validator: (value) => _phoneError,
              onChanged: (_) => _validatePhone(),
              textInputAction: TextInputAction.next,
            ),

            const SizedBox(height: 16),

            // حقل البريد الإلكتروني
            FormFieldWidget(
              controller: _emailController,
              label: 'البريد الإلكتروني',
              hintText: 'أدخل البريد الإلكتروني (اختياري)',
              prefixIcon: Icons.email_outlined,
              keyboardType: TextInputType.emailAddress,
              validator: (value) => _emailError,
              onChanged: (_) => _validateEmail(),
              textInputAction: TextInputAction.next,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات العمل
  Widget _buildWorkInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.work,
                  color: Colors.orange[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات العمل',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // حقل المنصب
            FormFieldWidget(
              controller: _positionController,
              label: 'المنصب',
              hintText: 'أدخل منصب الموظف (اختياري)',
              prefixIcon: Icons.badge_outlined,
              validator: (value) => _positionError,
              onChanged: (_) => _validatePosition(),
              textInputAction: TextInputAction.next,
            ),

            const SizedBox(height: 16),

            // حقل القسم
            FormFieldWidget(
              controller: _departmentController,
              label: 'القسم',
              hintText: 'أدخل قسم الموظف (اختياري)',
              prefixIcon: Icons.business_outlined,
              validator: (value) => _departmentError,
              onChanged: (_) => _validateDepartment(),
              textInputAction: TextInputAction.done,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر الحفظ الرئيسي
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton.icon(
            onPressed: (_isLoading || !_hasChanges) ? null : _saveEmployee,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.save),
            label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ التغييرات'),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  _hasChanges ? AppTheme.primaryColor : Colors.grey,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // زر الإلغاء
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton.icon(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            icon: const Icon(Icons.cancel_outlined),
            label: const Text('إلغاء'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.textSecondaryColor,
              side: BorderSide(color: Colors.grey[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// التحقق من صحة الاسم
  void _validateName() {
    setState(() {
      _nameError = null;

      final name = _nameController.text.trim();
      if (name.isEmpty) {
        _nameError = 'الاسم مطلوب';
      } else if (name.length < 2) {
        _nameError = 'الاسم يجب أن يكون أكثر من حرف واحد';
      } else if (name.length > 100) {
        _nameError = 'الاسم يجب أن يكون أقل من 100 حرف';
      }
    });

    // التحقق من التكرار إذا كان الاسم مختلفاً عن الاسم الأصلي
    if (_nameError == null &&
        _nameController.text.trim() != widget.employee.name &&
        !_isValidating) {
      _checkNameDuplication();
    }
  }

  /// التحقق من صحة رقم الهاتف
  void _validatePhone() {
    setState(() {
      _phoneError = null;

      final phone = _phoneController.text.trim();
      if (phone.isNotEmpty) {
        final phoneRegex = RegExp(r'^[0-9+\-\s()]+$');
        if (!phoneRegex.hasMatch(phone)) {
          _phoneError = 'رقم الهاتف غير صحيح';
        } else if (phone.length < 7 || phone.length > 20) {
          _phoneError = 'رقم الهاتف يجب أن يكون بين 7 و 20 رقم';
        }
      }
    });

    // التحقق من التكرار إذا كان الهاتف مختلفاً عن الهاتف الأصلي
    if (_phoneError == null &&
        _phoneController.text.trim() != (widget.employee.phone ?? '') &&
        _phoneController.text.trim().isNotEmpty &&
        !_isValidating) {
      _checkPhoneDuplication();
    }
  }

  /// التحقق من صحة البريد الإلكتروني
  void _validateEmail() {
    setState(() {
      _emailError = null;

      final email = _emailController.text.trim();
      if (email.isNotEmpty) {
        final emailRegex =
            RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
        if (!emailRegex.hasMatch(email)) {
          _emailError = 'البريد الإلكتروني غير صحيح';
        }
      }
    });

    // التحقق من التكرار إذا كان البريد مختلفاً عن البريد الأصلي
    if (_emailError == null &&
        _emailController.text.trim() != (widget.employee.email ?? '') &&
        _emailController.text.trim().isNotEmpty &&
        !_isValidating) {
      _checkEmailDuplication();
    }
  }

  /// التحقق من صحة المنصب
  void _validatePosition() {
    setState(() {
      _positionError = null;

      final position = _positionController.text.trim();
      if (position.isNotEmpty && position.length > 100) {
        _positionError = 'المنصب يجب أن يكون أقل من 100 حرف';
      }
    });
  }

  /// التحقق من صحة القسم
  void _validateDepartment() {
    setState(() {
      _departmentError = null;

      final department = _departmentController.text.trim();
      if (department.isNotEmpty && department.length > 100) {
        _departmentError = 'القسم يجب أن يكون أقل من 100 حرف';
      }
    });
  }

  /// التحقق من تكرار الاسم
  Future<void> _checkNameDuplication() async {
    final name = _nameController.text.trim();
    if (name.isEmpty) return;

    try {
      final exists = await _employeesService.isEmployeeNameExists(
        name,
        excludeId: widget.employee.id,
      );
      if (mounted) {
        setState(() {
          if (exists) {
            _nameError = 'يوجد موظف آخر بنفس الاسم';
          }
        });
      }
    } catch (e) {
      // تجاهل أخطاء التحقق من التكرار
    }
  }

  /// التحقق من تكرار رقم الهاتف
  Future<void> _checkPhoneDuplication() async {
    final phone = _phoneController.text.trim();
    if (phone.isEmpty) return;

    try {
      final exists = await _employeesService.isEmployeePhoneExists(
        phone,
        excludeId: widget.employee.id,
      );
      if (mounted) {
        setState(() {
          if (exists) {
            _phoneError = 'يوجد موظف آخر بنفس رقم الهاتف';
          }
        });
      }
    } catch (e) {
      // تجاهل أخطاء التحقق من التكرار
    }
  }

  /// التحقق من تكرار البريد الإلكتروني
  Future<void> _checkEmailDuplication() async {
    final email = _emailController.text.trim();
    if (email.isEmpty) return;

    try {
      final exists = await _employeesService.isEmployeeEmailExists(
        email,
        excludeId: widget.employee.id,
      );
      if (mounted) {
        setState(() {
          if (exists) {
            _emailError = 'يوجد موظف آخر بنفس البريد الإلكتروني';
          }
        });
      }
    } catch (e) {
      // تجاهل أخطاء التحقق من التكرار
    }
  }

  /// التحقق من صحة جميع الحقول
  bool _validateAllFields() {
    _validateName();
    _validatePhone();
    _validateEmail();
    _validatePosition();
    _validateDepartment();

    return _nameError == null &&
        _phoneError == null &&
        _emailError == null &&
        _positionError == null &&
        _departmentError == null;
  }

  /// حفظ تعديلات الموظف
  Future<void> _saveEmployee() async {
    if (!_validateAllFields()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تصحيح الأخطاء قبل الحفظ'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _isValidating = true;
    });

    try {
      // إنشاء موظف محدث
      final updatedEmployee = widget.employee.copyWith(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        position: _positionController.text.trim().isEmpty
            ? null
            : _positionController.text.trim(),
        department: _departmentController.text.trim().isEmpty
            ? null
            : _departmentController.text.trim(),
      );

      // التحقق النهائي من صحة البيانات
      final validationErrors = updatedEmployee.validate();
      if (validationErrors.isNotEmpty) {
        throw Exception(validationErrors.first);
      }

      // حفظ التعديلات في قاعدة البيانات
      await _employeesService.updateEmployee(updatedEmployee);

      if (mounted) {
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم تحديث بيانات الموظف "${updatedEmployee.name}" بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // العودة للشاشة السابقة مع إشارة النجاح
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isValidating = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث بيانات الموظف: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
