import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../constants/app_theme.dart';
import '../../services/reports_service.dart';
import '../../services/pdf_export_service.dart';
import 'report_preview_screen.dart';

/// شاشة التقارير الرئيسية
class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final ReportsService _reportsService = ReportsService();
  final PdfExportService _pdfExportService = PdfExportService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('التقارير'),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // مقدمة التقارير
          _buildIntroSection(),

          const SizedBox(height: 24),

          // أنواع التقارير
          _buildReportTypesSection(),

          const SizedBox(height: 24),

          // التقارير السريعة
          _buildQuickReportsSection(),
        ],
      ),
    );
  }

  /// بناء قسم المقدمة
  Widget _buildIntroSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor.withOpacity(0.1),
              AppTheme.secondaryColor.withOpacity(0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.assessment,
                  size: 32,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 12),
                const Text(
                  'تقارير المديونيات',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'احصل على تقارير شاملة ومفصلة عن جميع المديونيات، '
              'مع إمكانية التصدير والطباعة والمشاركة.',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم أنواع التقارير
  Widget _buildReportTypesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أنواع التقارير',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        AnimationLimiter(
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
            ),
            itemCount: ReportsService.getReportTypes().length,
            itemBuilder: (context, index) {
              final reportType = ReportsService.getReportTypes()[index];

              return AnimationConfiguration.staggeredGrid(
                position: index,
                duration: const Duration(milliseconds: 375),
                columnCount: 2,
                child: ScaleAnimation(
                  child: FadeInAnimation(
                    child: _buildReportTypeCard(reportType),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة نوع التقرير
  Widget _buildReportTypeCard(Map<String, dynamic> reportType) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _navigateToReportConfig(reportType['type']),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الأيقونة
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  _getIconData(reportType['icon']),
                  size: 24,
                  color: AppTheme.primaryColor,
                ),
              ),

              const SizedBox(height: 12),

              // الاسم
              Text(
                reportType['name'],
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 4),

              // الوصف
              Text(
                reportType['description'],
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم التقارير السريعة
  Widget _buildQuickReportsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التقارير السريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Column(
          children: [
            _buildQuickReportCard(
              title: 'تقرير اليوم',
              subtitle: 'مديونيات اليوم الحالي',
              icon: Icons.today,
              color: Colors.blue,
              onTap: () => _generateQuickReport(ReportPeriod.today),
            ),
            const SizedBox(height: 12),
            _buildQuickReportCard(
              title: 'تقرير هذا الشهر',
              subtitle: 'مديونيات الشهر الحالي',
              icon: Icons.calendar_month,
              color: Colors.green,
              onTap: () => _generateQuickReport(ReportPeriod.thisMonth),
            ),
            const SizedBox(height: 12),
            _buildQuickReportCard(
              title: 'المديونيات النشطة',
              subtitle: 'جميع المديونيات غير المدفوعة',
              icon: Icons.pending_actions,
              color: Colors.orange,
              onTap: () => _generateActiveDebtsReport(),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة التقرير السريع
  Widget _buildQuickReportCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // الأيقونة
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  icon,
                  size: 24,
                  color: color,
                ),
              ),

              const SizedBox(width: 16),

              // النص
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),

              // سهم
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على بيانات الأيقونة
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'assessment':
        return Icons.assessment;
      case 'people':
        return Icons.people;
      case 'badge':
        return Icons.badge;
      case 'pending':
        return Icons.pending;
      case 'check_circle':
        return Icons.check_circle;
      case 'summarize':
        return Icons.summarize;
      default:
        return Icons.description;
    }
  }

  /// التنقل لإعداد التقرير
  void _navigateToReportConfig(ReportType reportType) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReportConfigScreen(reportType: reportType),
      ),
    );

    if (result != null) {
      // معالجة نتيجة التقرير
    }
  }

  /// توليد تقرير سريع
  void _generateQuickReport(ReportPeriod period) async {
    try {
      _showLoadingDialog();

      final reportData = await _reportsService.generateReport(
        type: ReportType.comprehensive,
        period: period,
      );

      Navigator.of(context).pop(); // إغلاق حوار التحميل

      _navigateToReportPreview(reportData);
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق حوار التحميل
      _showErrorDialog('خطأ في توليد التقرير: ${e.toString()}');
    }
  }

  /// توليد تقرير المديونيات النشطة
  void _generateActiveDebtsReport() async {
    try {
      _showLoadingDialog();

      final reportData = await _reportsService.generateReport(
        type: ReportType.activeDebts,
        period: ReportPeriod.all,
      );

      Navigator.of(context).pop(); // إغلاق حوار التحميل

      _navigateToReportPreview(reportData);
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق حوار التحميل
      _showErrorDialog('خطأ في توليد التقرير: ${e.toString()}');
    }
  }

  /// التنقل لمعاينة التقرير
  void _navigateToReportPreview(ReportData reportData) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReportPreviewScreen(reportData: reportData),
      ),
    );
  }

  /// عرض حوار التحميل
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري توليد التقرير...'),
          ],
        ),
      ),
    );
  }

  /// عرض حوار الخطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}

/// شاشة إعداد التقرير
class ReportConfigScreen extends StatefulWidget {
  final ReportType reportType;

  const ReportConfigScreen({
    super.key,
    required this.reportType,
  });

  @override
  State<ReportConfigScreen> createState() => _ReportConfigScreenState();
}

class _ReportConfigScreenState extends State<ReportConfigScreen> {
  final ReportsService _reportsService = ReportsService();

  ReportPeriod _selectedPeriod = ReportPeriod.thisMonth;
  DateTime? _customStartDate;
  DateTime? _customEndDate;
  bool _isGenerating = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getReportTypeName(widget.reportType)),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات التقرير
          _buildReportInfo(),

          const SizedBox(height: 24),

          // اختيار الفترة الزمنية
          _buildPeriodSelection(),

          // التواريخ المخصصة
          if (_selectedPeriod == ReportPeriod.custom) ...[
            const SizedBox(height: 24),
            _buildCustomDateSelection(),
          ],
        ],
      ),
    );
  }

  /// بناء معلومات التقرير
  Widget _buildReportInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getReportTypeName(widget.reportType),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _getReportTypeDescription(widget.reportType),
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء اختيار الفترة الزمنية
  Widget _buildPeriodSelection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الفترة الزمنية',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            ...ReportsService.getReportPeriods().map((period) {
              return RadioListTile<ReportPeriod>(
                title: Text(period['name']),
                subtitle: Text(period['description']),
                value: period['period'],
                groupValue: _selectedPeriod,
                onChanged: (value) {
                  setState(() {
                    _selectedPeriod = value!;
                    if (value != ReportPeriod.custom) {
                      _customStartDate = null;
                      _customEndDate = null;
                    }
                  });
                },
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// بناء اختيار التواريخ المخصصة
  Widget _buildCustomDateSelection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التواريخ المخصصة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateField(
                    label: 'من تاريخ',
                    date: _customStartDate,
                    onTap: () => _selectStartDate(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateField(
                    label: 'إلى تاريخ',
                    date: _customEndDate,
                    onTap: () => _selectEndDate(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حقل التاريخ
  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              date != null
                  ? '${date.day}/${date.month}/${date.year}'
                  : 'اختر التاريخ',
              style: TextStyle(
                fontSize: 14,
                color:
                    date != null ? AppTheme.textPrimaryColor : Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الشريط السفلي
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        height: 50,
        child: ElevatedButton.icon(
          onPressed: _isGenerating ? null : _generateReport,
          icon: _isGenerating
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Icon(Icons.assessment),
          label: Text(_isGenerating ? 'جاري التوليد...' : 'توليد التقرير'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.comprehensive:
        return 'التقرير الشامل';
      case ReportType.byCustomer:
        return 'تقرير العملاء';
      case ReportType.byEmployee:
        return 'تقرير الموظفين';
      case ReportType.activeDebts:
        return 'المديونيات النشطة';
      case ReportType.paidDebts:
        return 'المديونيات المدفوعة';
      case ReportType.summary:
        return 'التقرير الملخص';
    }
  }

  /// الحصول على وصف نوع التقرير
  String _getReportTypeDescription(ReportType type) {
    switch (type) {
      case ReportType.comprehensive:
        return 'تقرير شامل يتضمن جميع المديونيات مع التفاصيل الكاملة';
      case ReportType.byCustomer:
        return 'تقرير مفصل عن مديونيات العملاء';
      case ReportType.byEmployee:
        return 'تقرير مفصل عن مديونيات الموظفين';
      case ReportType.activeDebts:
        return 'تقرير المديونيات غير المدفوعة فقط';
      case ReportType.paidDebts:
        return 'تقرير المديونيات المدفوعة فقط';
      case ReportType.summary:
        return 'ملخص سريع للمديونيات والإحصائيات';
    }
  }

  /// اختيار تاريخ البداية
  Future<void> _selectStartDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _customStartDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );

    if (pickedDate != null) {
      setState(() {
        _customStartDate = pickedDate;
        // التأكد من أن تاريخ النهاية ليس قبل تاريخ البداية
        if (_customEndDate != null && _customEndDate!.isBefore(pickedDate)) {
          _customEndDate = null;
        }
      });
    }
  }

  /// اختيار تاريخ النهاية
  Future<void> _selectEndDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _customEndDate ?? _customStartDate ?? DateTime.now(),
      firstDate: _customStartDate ?? DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );

    if (pickedDate != null) {
      setState(() {
        _customEndDate = pickedDate;
      });
    }
  }

  /// توليد التقرير
  Future<void> _generateReport() async {
    // التحقق من صحة التواريخ المخصصة
    if (_selectedPeriod == ReportPeriod.custom) {
      if (_customStartDate == null || _customEndDate == null) {
        _showErrorDialog('يرجى اختيار تاريخ البداية والنهاية');
        return;
      }

      if (_customEndDate!.isBefore(_customStartDate!)) {
        _showErrorDialog('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
        return;
      }
    }

    setState(() {
      _isGenerating = true;
    });

    try {
      final reportData = await _reportsService.generateReport(
        type: widget.reportType,
        period: _selectedPeriod,
        startDate: _customStartDate,
        endDate: _customEndDate,
      );

      if (mounted) {
        setState(() {
          _isGenerating = false;
        });

        // التنقل لمعاينة التقرير
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ReportPreviewScreen(reportData: reportData),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
        _showErrorDialog('خطأ في توليد التقرير: ${e.toString()}');
      }
    }
  }

  /// عرض حوار الخطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
